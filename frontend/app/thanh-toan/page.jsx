"use client";

import Image from "next/image";
import { useState, useRef, useContext, useEffect, Suspense } from "react";
import Button from "../../components/Button";
import TextField from "../../components/TextField";
import { UserContext } from "../../context/UserProvider";
import { useRouter, useSearchParams } from "next/navigation";
import logo from "../../public/images/Logo.png";
import strapi from "../api/strapi";
import { toast } from "react-hot-toast";

import LocationSelector from "../../components/LocationSelector";

// Create a separate component that uses useSearchParams
function CheckoutContent() {
  const { user, updateUser } = useContext(UserContext);
  const router = useRouter();
  const searchParams = useSearchParams();

  const [showLocationSelector, setShowLocationSelector] = useState(false);
  const locationSelectorRef = useRef(null);
  const addressInputRef = useRef(null);
  const [selectedLocation, setSelectedLocation] = useState("");
  const [detailAddress, setDetailAddress] = useState("");
  const [phone, setPhone] = useState("");
  const [isPolicyOpen, setIsPolicyOpen] = useState(false);
  const [discountCode, setDiscountCode] = useState("");
  const [formErrors, setFormErrors] = useState({
    fullname: "",
    phone: "",
    email: "",
    address: "",
    detailAddress: "",
  });
  const [voucherApplied, setVoucherApplied] = useState(false);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [discountError, setDiscountError] = useState("");
  const [isApplyingVoucher, setIsApplyingVoucher] = useState(false);
  const [remainingUses, setRemainingUses] = useState(null);

  useEffect(() => {
    // Debug: Log current cookie state
    if (typeof window !== 'undefined') {
      console.log('🍪 Current cookies:', document.cookie);
      console.log('👤 Current user:', user);
    }

    // Kiểm tra nếu là redirect từ PayOS
    const urlParams = new URLSearchParams(window.location.search);
    const status = urlParams.get('status');
    const cancel = urlParams.get('cancel');
    const orderCode = urlParams.get('orderCode');

    console.log('🔍 PayOS redirect params:', { status, cancel, orderCode });

    // Xử lý PayOS cancel (có thể là status=CANCELLED hoặc cancel=true)
    if (status === 'CANCELLED' || status === 'cancelled' || cancel === 'true') {
      console.log('💳 PayOS payment cancelled, redirecting to home');
      toast.error("Thanh toán đã bị hủy. Bạn có thể thử lại sau.");

      // Clear course data from localStorage since payment was cancelled
      if (typeof window !== 'undefined') {
        localStorage.removeItem('coursedata');
      }

      setTimeout(() => {
        router.push("/");
      }, 2000);
      return;
    }

    // Nếu có orderCode nhưng không có user, có thể là session timeout
    if (orderCode && !user) {
      console.log('⚠️ PayOS redirect with orderCode but no user - possible session timeout');
      toast.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại để tiếp tục.");

      // Store orderCode to localStorage for later use
      if (typeof window !== 'undefined') {
        localStorage.setItem('pendingOrderCode', orderCode);
      }

      setTimeout(() => {
        router.push("/dang-nhap");
      }, 2000);
      return;
    }

    if (!user && !orderCode) {
      console.log('🚫 No user found, clearing cookies and redirecting to login');
      // Clear any stale cookies before redirecting to login
      if (typeof window !== 'undefined') {
        // Clear potential stale cookies
        document.cookie = "access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        document.cookie = "user_data=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        document.cookie = "hasCompletedOrder=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        console.log('🧹 Cookies cleared');
      }
      router.push("/dang-nhap");
      return;
    }
  }, [user, router]);

  // State cho việc thanh toán
  const [isLoading, setIsLoading] = useState(false);
  const [paymentUrl, setPaymentUrl] = useState("");
  const [orderId, setOrderId] = useState(null);
  const [isFormValid, setIsFormValid] = useState(false);

  // Lấy thông tin từ URL search params và localStorage
  const defaultCourseInfo = {
    id: "",
    slug: "",
    title: "Đang tải...",
    tier_type: "",
    price: 0,
    duration: "0",
    total_lessons: 0,
  };

  const [courseInfo, setCourseInfo] = useState(defaultCourseInfo);

  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const CourseCheckoutData = localStorage.getItem("coursedata");
        if (!CourseCheckoutData) {
          router.push("/khoa-hoc");
          return;
        }

        const parsedData = JSON.parse(CourseCheckoutData);
        if (!parsedData || !parsedData.id) {
          router.push("/khoa-hoc");
          return;
        }

        setCourseInfo(parsedData);

        if (user?.phone) {
          setPhone(user.phone);
        }
      } catch (error) {
        console.error("Lỗi khi lấy thông tin khóa học:", error);
        router.push("/khoa-hoc");
      }
    }
  }, [searchParams, user, router]);

  // Rest of your component code...
  // [Copy all the remaining functions and state from your original component]

  // Include the handleAddressChange, handleDetailAddressChange, handleDiscountCodeChange, etc.
  const handleAddressChange = (address, type, option) => {
    setSelectedLocation(address);
    if (formErrors.address) {
      setFormErrors((prev) => ({ ...prev, address: null }));
    }
  };

  const handleDetailAddressChange = (e) => {
    setDetailAddress(e.target.value);
    if (formErrors.detailAddress) {
      setFormErrors((prev) => ({ ...prev, detailAddress: null }));
    }
  };

  const handleDiscountCodeChange = (e) => {
    setDiscountCode(e.target.value);
    setDiscountError("");
  };

  const handleClearDiscountCode = () => {
    setDiscountCode("");
    setVoucherApplied(false);
    calculateFinalPrice;
    setDiscountAmount(0);
    setDiscountError("");
  };

  const handleApplyVoucher = async () => {
    // Xác thực voucher bằng API call
    if (!discountCode.trim()) {
      setDiscountError("Vui lòng nhập mã giảm giá");
      return;
    }

    setIsApplyingVoucher(true);

    try {
      const courseData = localStorage.getItem("coursedata");
      if (!courseData) {
        setDiscountError("Không tìm thấy thông tin khóa học");
        setVoucherApplied(false);
        setDiscountAmount(0);
        setIsApplyingVoucher(false);
        return;
      }

      const parsedData = JSON.parse(courseData);
      // Chuyển đổi mã voucher thành chuỗi để đảm bảo xử lý đúng cả số và chữ
      const voucherCodeString = discountCode.toString().trim();
      const response = await strapi.vouchers.validateVoucher(
        voucherCodeString,
        parsedData.id,
        user?.email // Truyền email để kiểm tra voucher có áp dụng cho email này không
      );

      if (response && response.valid) {
        let discount = 0;
        if (response.discount_percent) {
          discount = Math.round(
            courseInfo.price * (response.discount_percent / 100)
          );
        } else if (response.discount_amount) {
          discount = response.discount_amount;
        }

        setDiscountAmount(discount);
        setVoucherApplied(true);
        setDiscountError("");

        // Lưu và hiển thị thông báo thành công với số lượt sử dụng còn lại
        if (response.remaining_uses !== null) {
          setRemainingUses(response.remaining_uses);
          toast.success(
            `Áp dụng mã giảm giá thành công! Còn lại ${response.remaining_uses} lượt sử dụng.`
          );
        } else {
          setRemainingUses(null);
          toast.success("Áp dụng mã giảm giá thành công!");
        }

        // Hiển thị thông tin giảm giá
        if (response.discount_percent) {
          toast.success(
            `Giảm giá ${response.discount_percent}% cho khóa học này.`
          );
        } else if (response.discount_amount) {
          toast.success(
            `Giảm giá ${response.discount_amount.toLocaleString(
              "vi-VN"
            )}đ cho khóa học này.`
          );
        }
      } else {
        // Hiển thị thông báo lỗi cụ thể nếu có
        if (response && response.message) {
          setDiscountError(response.message);

          // Xử lý các loại lỗi cụ thể
          if (response.message === "Voucher đã hết lượt sử dụng") {
            toast.error(
              "Voucher đã hết lượt sử dụng. Vui lòng sử dụng mã khác."
            );
          } else if (
            response.message === "Voucher không áp dụng cho email này"
          ) {
            toast.error(
              "Voucher này không áp dụng cho email của bạn. Vui lòng sử dụng mã khác."
            );
          } else if (response.message === "Email này đã sử dụng voucher") {
            toast.error(
              "Email của bạn đã sử dụng voucher này. Mỗi email chỉ được sử dụng voucher một lần."
            );
          } else if (
            response.message === "Voucher không áp dụng cho khóa học này"
          ) {
            toast.error(
              "Voucher không áp dụng cho khóa học này. Vui lòng sử dụng mã khác."
            );
          } else {
            toast.error(response.message);
          }
        } else {
          setDiscountError("Mã giảm giá không hợp lệ hoặc đã hết hạn");
          toast.error("Mã giảm giá không hợp lệ hoặc đã hết hạn");
        }
        setVoucherApplied(false);
        setDiscountAmount(0);
      }
    } catch (error) {
      console.error("Lỗi khi áp dụng voucher:", error);

      // Xử lý lỗi cụ thể từ API
      if (error.response?.data?.error?.message) {
        const errorMessage = error.response.data.error.message;
        setDiscountError(errorMessage);

        // Xử lý các loại lỗi cụ thể
        if (errorMessage === "Voucher đã hết lượt sử dụng") {
          toast.error("Voucher đã hết lượt sử dụng. Vui lòng sử dụng mã khác.");
        } else if (errorMessage === "Voucher không áp dụng cho email này") {
          toast.error(
            "Voucher này không áp dụng cho email của bạn. Vui lòng sử dụng mã khác."
          );
        } else if (errorMessage === "Email này đã sử dụng voucher") {
          toast.error(
            "Email của bạn đã sử dụng voucher này. Mỗi email chỉ được sử dụng voucher một lần."
          );
        } else if (errorMessage === "Voucher không áp dụng cho khóa học này") {
          toast.error(
            "Voucher không áp dụng cho khóa học này. Vui lòng sử dụng mã khác."
          );
        } else {
          toast.error(errorMessage);
        }
      } else if (error.message) {
        setDiscountError(error.message);
        toast.error(error.message);
      } else {
        setDiscountError("Mã giảm giá không hợp lệ hoặc đã hết hạn");
        toast.error("Mã giảm giá không hợp lệ hoặc đã hết hạn");
      }

      setVoucherApplied(false);
      setDiscountAmount(0);
    } finally {
      setIsApplyingVoucher(false);
    }
  };

  useEffect(() => {
    const isPhoneValid = phone && /^\d{10}$/.test(phone);
    const isAddressValid = selectedLocation && detailAddress;

    setIsFormValid(isPhoneValid && isAddressValid);
  }, [phone, selectedLocation, detailAddress]);

  const calculateFinalPrice = () => {
    // Đảm bảo price không phải undefined hoặc null
    const basePrice = courseInfo.price || 0;

    if (voucherApplied && discountAmount > 0) {
      const discountedPrice = basePrice - discountAmount;
      // Đảm bảo giá không âm và tối thiểu là 2000đ
      return discountedPrice > 2000 ? discountedPrice : 2000;
    }
    // Đảm bảo giá tối thiểu là 2000đ
    return basePrice > 0 ? basePrice : 2000;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "detailAddress") {
      setDetailAddress(value);
    }
    if (name === "phone") {
      setPhone(value);
    }

    setFormErrors((prev) => ({
      ...prev,
      [name]: "",
    }));
  };

  const handleClearField = (fieldName) => {
    if (fieldName === "detailAddress") {
      setDetailAddress("");
    }

    setFormErrors((prev) => ({
      ...prev,
      [fieldName]: "",
    }));
  };

  const handlePayment = async () => {
    if (typeof window === "undefined") return;

    try {
      setIsLoading(true);
      if (!user) {
        router.push("/dang-nhap");
        return;
      }

      const courseData = localStorage.getItem("coursedata");
      if (!courseData) {
        toast.error("Vui lòng chọn khóa học để thanh toán");
        return;
      }

      if (!phone || !selectedLocation || !detailAddress) {
        if (!phone) {
          setFormErrors((prev) => ({
            ...prev,
            phone: "Vui lòng nhập số điện thoại",
          }));
        }
        if (!selectedLocation) {
          setFormErrors((prev) => ({
            ...prev,
            address: "Vui lòng chọn địa chỉ",
          }));
        }
        if (!detailAddress) {
          setFormErrors((prev) => ({
            ...prev,
            detailAddress: "Vui lòng nhập địa chỉ chi tiết",
          }));
        }
        toast.error("Vui lòng điền đầy đủ thông tin trước khi thanh toán");
        return;
      }

      if (user && phone !== user.phone) {
        await updateUser({ phone });
      }

      const parsedData = JSON.parse(courseData);

      const fullAddress = `${detailAddress}, ${selectedLocation}`;

      const orderResponse = await strapi.orders.createOrder({
        userId: user.id,
        courseId: parsedData.id,
        courseTierId: parsedData.tier_id,
        amount: calculateFinalPrice(), // Sử dụng giá đã giảm
        discountAmount: discountAmount, // Thêm số tiền giảm giá
        voucherCode: voucherApplied ? discountCode : null, // Thêm mã voucher nếu được áp dụng
        deliveryAddress: fullAddress,
      });

      if (!orderResponse.data.id) {
        throw new Error("Không nhận được order ID");
      }

      // Tạo mô tả cho thanh toán
      const description = `Thanh toán khóa học: ${courseInfo.title || ""}`;

      const paymentResponse = await strapi.payment.createPaymentLink({
        orderId: orderResponse.data.id,
        amount: calculateFinalPrice(), // Sử dụng giá đã giảm
        courseId: parsedData.id,
        description: description,
      });

      // Nếu có voucher được áp dụng, tăng số lần sử dụng
      if (voucherApplied && discountCode) {
        try {
          // Gọi API để tăng số lần sử dụng voucher, truyền thêm userId nếu có
          const incrementResponse = await strapi.vouchers.incrementVoucherUses(
            discountCode,
            user?.id,
            user?.email // Truyền email để theo dõi email đã sử dụng voucher
          );

          if (incrementResponse.success === false) {
            // Xử lý trường hợp voucher đã đạt giới hạn nhưng API trả về thành công
            console.log("Thông báo từ API:", incrementResponse.message);

            // Xử lý các loại lỗi cụ thể
            if (
              incrementResponse.message === "Voucher đã đạt giới hạn sử dụng"
            ) {
              toast.error(
                "Voucher đã đạt giới hạn sử dụng, nhưng thanh toán vẫn được tiếp tục."
              );
            } else if (
              incrementResponse.message ===
              "Voucher không áp dụng cho email này"
            ) {
              toast.error(
                "Voucher này không áp dụng cho email của bạn, nhưng thanh toán vẫn được tiếp tục."
              );
            } else if (
              incrementResponse.message === "Email này đã sử dụng voucher"
            ) {
              toast.error(
                "Email của bạn đã sử dụng voucher này, nhưng thanh toán vẫn được tiếp tục."
              );
            } else {
              toast.error(
                `${incrementResponse.message}, nhưng thanh toán vẫn được tiếp tục.`
              );
            }
          } else {
            console.log("Đã tăng số lần sử dụng voucher:", discountCode);
          }
        } catch (error) {
          console.error("Lỗi khi tăng số lần sử dụng voucher:", error);
          // Không throw error ở đây để không ảnh hưởng đến luồng thanh toán
          toast.error(
            "Có lỗi khi cập nhật số lần sử dụng voucher, nhưng thanh toán vẫn được tiếp tục."
          );
        }
      }

      if (paymentResponse.paymentUrl || paymentResponse.checkoutUrl) {
        if (typeof window !== "undefined") {
          window.location.href =
            paymentResponse.paymentUrl || paymentResponse.checkoutUrl;
        }
      } else {
        throw new Error("Không nhận được URL thanh toán");
      }
    } catch (error) {
      console.error("Lỗi khi thanh toán:", error);

      // Hiển thị thông báo lỗi chi tiết hơn
      if (error.response) {
        console.error("Chi tiết lỗi:", error.response.data);

        // Xử lý lỗi cụ thể từ API
        if (error.response.status === 400) {
          // Lỗi Bad Request
          const errorMessage =
            error.response.data?.error?.message ||
            "Dữ liệu thanh toán không hợp lệ. Vui lòng thử lại.";
          toast.error(errorMessage);

          // Log thêm thông tin để debug
          console.log("Thông tin thanh toán:", {
            amount: calculateFinalPrice(),
            discountAmount: discountAmount,
            voucherApplied: voucherApplied,
          });
        } else {
          // Các lỗi khác
          const errorMessage =
            error.response.data?.error?.message ||
            "Có lỗi xảy ra khi thanh toán. Vui lòng thử lại sau.";
          toast.error(errorMessage);
        }
      } else if (error.message) {
        // Lỗi từ JavaScript
        toast.error(`Lỗi: ${error.message}`);
      } else {
        // Lỗi không xác định
        toast.error("Có lỗi xảy ra khi thanh toán. Vui lòng thử lại sau.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Return the UI part
  return (
    <div className="min-h-screen bg-[#FFFFFF] overflow-x-hidden">
      {/* Your entire component JSX */}
      <div className="flex flex-col min-h-screen">
        <div className="flex justify-center items-center py-12">
          <div className="flex justify-center items-center flex-col w-full max-w-[994px] px-4">
            <div className="mb-8">
              <Image
                src={logo}
                alt="logo"
                width={130}
                height={48}
                priority={true}
                onClick={() => {
                  if (typeof window !== "undefined") {
                    localStorage.removeItem("coursedata");
                    router.push("/");
                  }
                }}
              />
            </div>

            {/* Rest of your JSX... */}
            <div className="flex items-center justify-between w-full px-2 mb-8">
              <h1 className="text-2xl text-[#181D27] font-semibold">
                Thanh toán
              </h1>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                className="cursor-pointer"
                onClick={() => {
                  if (typeof window !== "undefined") {
                    localStorage.removeItem("coursedata");
                    router.back();
                  }
                }}
              >
                <path
                  d="M15 5L5 15M5 5L15 15"
                  stroke="#535862"
                  strokeWidth="1.66667"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>

            {/* Rest of your form and UI components - copy from original component */}
            <div className="flex justify-between w-full gap-4">
              <div className="flex flex-col gap-4 w-full md:w-[590px]">
                <div className="flex flex-col gap-8">
                  <div className="flex flex-col gap-8 p-6 border border-[#E9EAEB] rounded-2xl">
                    <div className="flex flex-col gap-6">
                      <h3 className="text-lg text-[#181D27] font-semibold">
                        Thông tin cá nhân
                      </h3>

                      <div className="space-y-4">
                        <TextField
                          label="Họ và Tên"
                          name="fullname"
                          type="text"
                          value={user?.fullname || ""}
                          onChange={handleInputChange}
                          placeholder="Nhập họ và tên"
                          required={true}
                          error={formErrors.fullname}
                          disabled={true}
                        />

                        <TextField
                          label="Số điện thoại"
                          name="phone"
                          type="text"
                          value={phone}
                          onChange={handleInputChange}
                          placeholder="Nhập số điện thoại"
                          required={true}
                          error={formErrors.phone}
                          onClear={() => handleClearField("phone")}
                        />

                        <TextField
                          label="Email"
                          name="email"
                          type="text"
                          value={user?.email || ""}
                          placeholder="Nhập địa chỉ email"
                          required={true}
                          error={formErrors.email}
                          disabled={true}
                        />
                      </div>
                    </div>
                    <div className="flex flex-col gap-6">
                      <h3 className="md:hidden text-lg text-[#181D27] font-semibold">
                        Thông tin giao tài liệu
                      </h3>
                      <div className="md:hidden space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-[#414651] mb-[6px]">
                            Tỉnh/Thành phố, Quận/Huyện, Phường/Xã{" "}
                            <span className="text-[#D92D20]">*</span>
                          </label>
                          <div className="relative" ref={addressInputRef}>
                            <input
                              type="text"
                              name="address"
                              value={selectedLocation}
                              onClick={() => setShowLocationSelector(true)}
                              className="w-full px-[14px] py-[10px] border border-[#D5D7DA] rounded-lg focus:ring-2 focus:ring-[#45BF76] focus:outline-none cursor-pointer"
                              placeholder="Chọn địa chỉ"
                              readOnly
                              required
                            />
                            {showLocationSelector && (
                              <>
                                <div
                                  className="fixed inset-0 bg-[#181D27] max-md:bg-opacity-50 bg-opacity-0 z-40"
                                  onClick={() => setShowLocationSelector(false)}
                                />
                                <div
                                  ref={locationSelectorRef}
                                  className="absolute left-0 right-0 z-50"
                                >
                                  <LocationSelector
                                    onAddressChange={handleAddressChange}
                                    onClose={() =>
                                      setShowLocationSelector(false)
                                    }
                                    currentAddress={selectedLocation}
                                  />
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                        <TextField
                          label="Địa chỉ chi tiết"
                          name="detailAddress"
                          value={detailAddress}
                          onChange={handleInputChange}
                          placeholder="Nhập địa chỉ chi tiết"
                          required={true}
                          error={formErrors.detailAddress}
                          onClear={() => handleClearField("detailAddress")}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="md:block hidden flex-col gap-6 p-6 border border-[#E9EAEB] rounded-2xl">
                    <h3 className="text-lg text-[#181D27] font-semibold">
                      Thông tin giao tài liệu
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-[#414651] mb-[6px]">
                          Tỉnh/Thành phố, Quận/Huyện, Phường/Xã{" "}
                          <span className="text-[#D92D20]">*</span>
                        </label>
                        <div className="relative" ref={addressInputRef}>
                          <input
                            type="text"
                            name="address"
                            value={selectedLocation}
                            onClick={() => setShowLocationSelector(true)}
                            className="w-full px-[14px] py-[10px] border border-[#D5D7DA] rounded-lg focus:ring-2 focus:ring-[#45BF76] focus:outline-none cursor-pointer"
                            placeholder="Chọn địa chỉ"
                            readOnly
                            required
                          />
                          {showLocationSelector && (
                            <>
                              <div
                                className="fixed inset-0 bg-[#181D27] max-md:bg-opacity-50 bg-opacity-0 z-40"
                                onClick={() => setShowLocationSelector(false)}
                              />
                              <div
                                ref={locationSelectorRef}
                                className="absolute left-0 right-0 z-50"
                              >
                                <LocationSelector
                                  onAddressChange={handleAddressChange}
                                  onClose={() => setShowLocationSelector(false)}
                                  currentAddress={selectedLocation}
                                />
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                      <TextField
                        label="Địa chỉ chi tiết"
                        name="detailAddress"
                        value={detailAddress}
                        onChange={handleInputChange}
                        placeholder="Nhập địa chỉ chi tiết"
                        required={true}
                        error={formErrors.detailAddress}
                        onClear={() => handleClearField("detailAddress")}
                      />
                    </div>
                  </div>
                  <div className="md:hidden flex flex-col gap-6 p-6  border border-[#E9EAEB] rounded-2xl">
                    <div className="rounded-lg overflow-hidden  relative">
                      <div className="w-full h-[180px] relative">
                        <Image
                          src="/images/Image-checkout.png"
                          alt="Course"
                          fill
                          priority={true}
                          className="object-cover"
                        />
                      </div>
                      {/* Text Overlay */}
                      <div className="absolute inset-0 p-4 flex flex-col justify-end">
                        <h3 className="text-[#FFFFFF] text-base font-semibold mb-1">
                          {courseInfo.title || "Đang tải..."} -{" "}
                          {courseInfo.tier_type || ""}
                        </h3>

                        <div className="flex items-center gap-3 text-[#D5D7DA] text-sm font-normal mt-1">
                          <div className="flex items-center gap-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                            >
                              <path
                                d="M7.99998 13.3334H3.46665C2.71991 13.3334 2.34654 13.3334 2.06133 13.188C1.81044 13.0602 1.60647 12.8562 1.47864 12.6053C1.33331 12.3201 1.33331 11.9468 1.33331 11.2V4.80002C1.33331 4.05328 1.33331 3.67992 1.47864 3.3947C1.60647 3.14382 1.81044 2.93984 2.06133 2.81201C2.34654 2.66669 2.71991 2.66669 3.46665 2.66669H3.73331C5.22679 2.66669 5.97352 2.66669 6.54395 2.95734C7.04572 3.213 7.45367 3.62095 7.70933 4.12271C7.99998 4.69314 7.99998 5.43988 7.99998 6.93335M7.99998 13.3334V6.93335M7.99998 13.3334H12.5333C13.28 13.3334 13.6534 13.3334 13.9386 13.188C14.1895 13.0602 14.3935 12.8562 14.5213 12.6053C14.6666 12.3201 14.6666 11.9468 14.6666 11.2V4.80002C14.6666 4.05328 14.6666 3.67992 14.5213 3.3947C14.3935 3.14382 14.1895 2.93984 13.9386 2.81201C13.6534 2.66669 13.28 2.66669 12.5333 2.66669H12.2666C10.7732 2.66669 10.0264 2.66669 9.45601 2.95734C8.95424 3.213 8.54629 3.62095 8.29063 4.12271C7.99998 4.69314 7.99998 5.43988 7.99998 6.93335"
                                stroke="#D5D7DA"
                                strokeWidth="1.66667"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                            {courseInfo.total_lessons || 0} buổi
                          </div>
                          <div className="flex items-center gap-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                            >
                              <path
                                d="M9.99999 5.33334V9.33334L12 10.3333M16 9.33334C16 13.0152 12.9819 16.0333 9.3 16.0333C5.61807 16.0333 2.6 13.0152 2.6 9.33334C2.6 5.65143 5.61807 2.63336 9.3 2.63336C12.9819 2.63336 16 5.65143 16 9.33334Z"
                                stroke="#D5D7DA"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                            {courseInfo.duration || "0 phút"}/buổi
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="">
                      <h2 className="text-lg font-semibold text-[#181D27] mb-4">
                        Phương thức thanh toán
                      </h2>

                      <div className="grid grid-cols-1  gap-4">
                        <button
                          onClick={handlePayment}
                          disabled={isLoading}
                          className="w-full h-full flex flex-col items-center p-4 border-2 border-[#E9EAEB] focus:border-2 focus:border-[#299D55] rounded-xl bg-[#FFFFFF] relative"
                        >
                          <div className="flex flex-col items-center gap-4 h-full">
                            <div className="bg-[#FFFFFF] rounded-xl">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="32"
                                height="32"
                                viewBox="0 0 32 32"
                                fill="none"
                              >
                                <path
                                  fillRule="evenodd"
                                  clipRule="evenodd"
                                  d="M22.3333 31.3333H28.3333C29.9903 31.3333 31.3333 29.9903 31.3333 28.3333V22.3333C31.3333 21.7813 30.8853 21.3333 30.3333 21.3333C29.7813 21.3333 29.3333 21.7813 29.3333 22.3333V28.3333C29.3333 28.8853 28.8853 29.3333 28.3333 29.3333H22.3333C21.7813 29.3333 21.3333 29.7813 21.3333 30.3333C21.3333 30.8853 21.7813 31.3333 22.3333 31.3333ZM10.3333 29.3333H4.33331C3.78131 29.3333 3.33331 28.8853 3.33331 28.3333V22.3333C3.33331 21.7813 2.88531 21.3333 2.33331 21.3333C1.78131 21.3333 1.33331 21.7813 1.33331 22.3333V28.3333C1.33331 29.9903 2.67631 31.3333 4.33331 31.3333H10.3333C10.8853 31.3333 11.3333 30.8853 11.3333 30.3333C11.3333 29.7813 10.8853 29.3333 10.3333 29.3333ZM15.3333 20.3333V26.3333C15.3333 26.8853 15.7813 27.3333 16.3333 27.3333C16.8853 27.3333 17.3333 26.8853 17.3333 26.3333V20.3333C17.3333 19.7813 16.8853 19.3333 16.3333 19.3333C15.7813 19.3333 15.3333 19.7813 15.3333 20.3333ZM13.3333 20.3333C13.3333 19.7813 12.8853 19.3333 12.3333 19.3333H6.33331C5.78131 19.3333 5.33331 19.7813 5.33331 20.3333V26.3333C5.33331 26.8853 5.78131 27.3333 6.33331 27.3333H12.3333C12.8853 27.3333 13.3333 26.8853 13.3333 26.3333V20.3333ZM27.3333 20.3333C27.3333 19.7813 26.8853 19.3333 26.3333 19.3333H20.3333C19.7813 19.3333 19.3333 19.7813 19.3333 20.3333V26.3333C19.3333 26.8853 19.7813 27.3333 20.3333 27.3333H26.3333C26.8853 27.3333 27.3333 26.8853 27.3333 26.3333V20.3333ZM11.3333 21.3333V25.3333H7.33331V21.3333H11.3333ZM25.3333 21.3333V25.3333H21.3333V21.3333H25.3333ZM15.3333 17.3333H16.3333C16.8853 17.3333 17.3333 16.8853 17.3333 16.3333C17.3333 15.7813 16.8853 15.3333 16.3333 15.3333H15.3333C14.7813 15.3333 14.3333 15.7813 14.3333 16.3333C14.3333 16.8853 14.7813 17.3333 15.3333 17.3333ZM6.33331 17.3333H11.3333C11.8853 17.3333 12.3333 16.8853 12.3333 16.3333C12.3333 15.7813 11.8853 15.3333 11.3333 15.3333H6.33331C5.78131 15.3333 5.33331 15.7813 5.33331 16.3333C5.33331 16.8853 5.78131 17.3333 6.33331 17.3333ZM24.3333 15.3333H25.3333V16.3333C25.3333 16.8853 25.7813 17.3333 26.3333 17.3333C26.8853 17.3333 27.3333 16.8853 27.3333 16.3333V14.3333C27.3333 13.7813 26.8853 13.3333 26.3333 13.3333H24.3333C23.7813 13.3333 23.3333 13.7813 23.3333 14.3333C23.3333 14.8853 23.7813 15.3333 24.3333 15.3333ZM21.3333 16.3333V11.3333H22.3333C22.8853 11.3333 23.3333 10.8853 23.3333 10.3333C23.3333 9.78131 22.8853 9.33331 22.3333 9.33331H20.3333C19.7813 9.33331 19.3333 9.78131 19.3333 10.3333V16.3333C19.3333 16.8853 19.7813 17.3333 20.3333 17.3333C20.8853 17.3333 21.3333 16.8853 21.3333 16.3333ZM15.3333 10.3333V12.3333C15.3333 12.8853 15.7813 13.3333 16.3333 13.3333C16.8853 13.3333 17.3333 12.8853 17.3333 12.3333V10.3333C17.3333 9.78131 16.8853 9.33331 16.3333 9.33331C15.7813 9.33331 15.3333 9.78131 15.3333 10.3333ZM13.3333 6.33331C13.3333 5.78131 12.8853 5.33331 12.3333 5.33331H6.33331C5.78131 5.33331 5.33331 5.78131 5.33331 6.33331V12.3333C5.33331 12.8853 5.78131 13.3333 6.33331 13.3333H12.3333C12.8853 13.3333 13.3333 12.8853 13.3333 12.3333V6.33331ZM10.3333 1.33331H4.33331C2.67631 1.33331 1.33331 2.67631 1.33331 4.33331V10.3333C1.33331 10.8853 1.78131 11.3333 2.33331 11.3333C2.88531 11.3333 3.33331 10.8853 3.33331 10.3333V4.33331C3.33331 3.78131 3.78131 3.33331 4.33331 3.33331H10.3333C10.8853 3.33331 11.3333 2.88531 11.3333 2.33331C11.3333 1.78131 10.8853 1.33331 10.3333 1.33331ZM11.3333 7.33331V11.3333H7.33331V7.33331H11.3333ZM22.3333 3.33331H28.3333C28.8853 3.33331 29.3333 3.78131 29.3333 4.33331V10.3333C29.3333 10.8853 29.7813 11.3333 30.3333 11.3333C30.8853 11.3333 31.3333 10.8853 31.3333 10.3333V4.33331C31.3333 2.67631 29.9903 1.33331 28.3333 1.33331H22.3333C21.7813 1.33331 21.3333 1.78131 21.3333 2.33331C21.3333 2.88531 21.7813 3.33331 22.3333 3.33331ZM16.3333 7.33331H25.3333V10.3333C25.3333 10.8853 25.7813 11.3333 26.3333 11.3333C26.8853 11.3333 27.3333 10.8853 27.3333 10.3333V6.33331C27.3333 5.78131 26.8853 5.33331 26.3333 5.33331H16.3333C15.7813 5.33331 15.3333 5.78131 15.3333 6.33331C15.3333 6.88531 15.7813 7.33331 16.3333 7.33331Z"
                                  fill="black"
                                />
                              </svg>
                            </div>
                            <span className="text-sm text-center">
                              Quét mã QR thông qua ứng dụng Ngân Hàng hoặc ví
                              điện tử Momo, ZaloPay
                            </span>
                          </div>
                        </button>
                      </div>
                    </div>

                    <div className="flex flex-col gap-4">
                      <h2 className="text-lg font-semibold text-[#181D27] ">
                        Ưu đãi
                      </h2>
                      <div className="flex flex-col gap-2">
                        <div className="flex gap-2 items-start">
                          <TextField
                            placeholder="Nhập mã ưu đãi"
                            name="discountCode"
                            className="flex-1"
                            value={discountCode}
                            onChange={handleDiscountCodeChange}
                            onClear={
                              voucherApplied ? null : handleClearDiscountCode
                            }
                            error={discountError}
                            disabled={voucherApplied}
                          />
                          <div className="flex-shrink-0 self-start pt-[1px]">
                            <Button
                              variant="primary"
                              className="h-[42px] min-w-[90px]"
                              onClick={handleApplyVoucher}
                              disabled={
                                isApplyingVoucher || !discountCode.trim()
                              }
                            >
                              {isApplyingVoucher
                                ? "Đang áp dụng..."
                                : voucherApplied
                                ? "Đã áp dụng"
                                : "Áp dụng"}
                            </Button>
                          </div>
                        </div>

                        {/* Hiển thị số lượt sử dụng còn lại */}
                        {voucherApplied && remainingUses !== null && (
                          <div className="text-sm text-green-600 mt-1">
                            Còn lại {remainingUses} lượt sử dụng
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col gap-4">
                      <div className="flex items-center justify-between">
                        <h2 className="text-base font-normal text-[#181D27] ">
                          Tổng tiền
                        </h2>
                        <span className="text-base font-normal text-[#181D27] ">
                          {(courseInfo.price || 0).toLocaleString("vi-VN")}đ
                        </span>
                      </div>

                      {voucherApplied && discountAmount > 0 && (
                        <div className="flex items-center justify-between">
                          <h2 className="text-base font-normal text-[#181D27]">
                            Giảm giá
                          </h2>
                          <span className="text-base font-normal text-green-600">
                            -{(discountAmount || 0).toLocaleString("vi-VN")}đ
                          </span>
                        </div>
                      )}

                      <div className="border border-[#E9EAEB]"></div>
                      <div className="flex items-center justify-between">
                        <span className="text-base font-semibold text-[#181D27] ">
                          Tổng thanh toán
                        </span>
                        <span className="text-base font-semibold text-[#181D27] ">
                          {calculateFinalPrice().toLocaleString("vi-VN")}đ
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col gap-6 p-6 border border-[#E9EAEB] rounded-2xl">
                    <div
                      className="flex items-center justify-between"
                      onClick={() => setIsPolicyOpen(!isPolicyOpen)}
                    >
                      <h3 className="text-lg text-[#181D27] font-semibold">
                        Chính sách
                      </h3>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d={
                            isPolicyOpen
                              ? "M15 12.5L10 7.5L5 12.5"
                              : "M5 7.5L10 12.5L15 7.5"
                          }
                          stroke="#A4A7AE"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>

                    {isPolicyOpen && (
                      <div className="text-[#181D27] text-base font-normal">
                        <p className="mb-4">
                          Team Thầy Ba cam kết bảo vệ quyền lợi và tạo môi
                          trường học tập tích cực cho tất cả học viên:
                        </p>

                        <ul className="space-y-4">
                          <li className="flex items-start gap-2">
                            <span className="text-[#181D27] mt-1">•</span>
                            <span>
                              Bảo mật thông tin: Team Thầy Ba thu thập thông tin
                              cá nhân chỉ nhằm phục vụ việc học tập, không chia
                              sẻ với bên thứ ba cho mục đích thương mại, và áp
                              dụng các biện pháp bảo mật nghiêm ngặt.
                            </span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-[#181D27] mt-1">•</span>
                            <span>
                              Quyền lợi và trách nhiệm: Học viên được sử dụng
                              khóa học cho mục đích cá nhân, không được chia sẻ
                              tài khoản hoặc phát tán nội dung, và cần tuân thủ
                              quy tắc cộng đồng.
                            </span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-[#181D27] mt-1">•</span>
                            <span>
                              Chính sách thanh toán: Học phí cần thanh toán đầy
                              đủ trước khi được kích hoạt tài khoản và sẽ không
                              được hoàn lại sau khi đã truy cập nội dung khóa
                              học.
                            </span>
                          </li>
                        </ul>

                        <div className="mt-4">
                          <a
                            href="chinh-sach-bao-mat"
                            className="text-[#181D27] underline "
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            Xem chi tiết Điều khoản sử dụng
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="w-full h-full md:w-[388px] md:block hidden bg-[#FFFFFF] rounded-2xl border border-[#E9EAEB] p-6">
                {/* Course image and info */}
                <div className="rounded-lg overflow-hidden mb-6 relative">
                  <div className="w-full h-[180px] relative">
                    <Image
                      src="/images/Image-checkout.png"
                      alt="Course"
                      fill
                      priority={true}
                      className="object-cover"
                    />
                  </div>
                  {/* Text Overlay */}
                  <div className="absolute inset-0 p-4 flex flex-col justify-end">
                    <h3 className="text-[#FFFFFF] text-base font-semibold mb-1">
                      {courseInfo.title || "Đang tải..."} -{" "}
                      {courseInfo.tier_type || ""}
                    </h3>
                    <div className="flex items-center gap-3 text-[#D5D7DA] text-sm font-normal">
                      <div className="flex items-center gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                        >
                          <path
                            d="M7.99998 13.3334H3.46665C2.71991 13.3334 2.34654 13.3334 2.06133 13.188C1.81044 13.0602 1.60647 12.8562 1.47864 12.6053C1.33331 12.3201 1.33331 11.9468 1.33331 11.2V4.80002C1.33331 4.05328 1.33331 3.67992 1.47864 3.3947C1.60647 3.14382 1.81044 2.93984 2.06133 2.81201C2.34654 2.66669 2.71991 2.66669 3.46665 2.66669H3.73331C5.22679 2.66669 5.97352 2.66669 6.54395 2.95734C7.04572 3.213 7.45367 3.62095 7.70933 4.12271C7.99998 4.69314 7.99998 5.43988 7.99998 6.93335M7.99998 13.3334V6.93335M7.99998 13.3334H12.5333C13.28 13.3334 13.6534 13.3334 13.9386 13.188C14.1895 13.0602 14.3935 12.8562 14.5213 12.6053C14.6666 12.3201 14.6666 11.9468 14.6666 11.2V4.80002C14.6666 4.05328 14.6666 3.67992 14.5213 3.3947C14.3935 3.14382 14.1895 2.93984 13.9386 2.81201C13.6534 2.66669 13.28 2.66669 12.5333 2.66669H12.2666C10.7732 2.66669 10.0264 2.66669 9.45601 2.95734C8.95424 3.213 8.54629 3.62095 8.29063 4.12271C7.99998 4.69314 7.99998 5.43988 7.99998 6.93335"
                            stroke="#D5D7DA"
                            strokeWidth="1.66667"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        {courseInfo.total_lessons || 0} buổi
                      </div>
                      <div className="flex items-center gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                        >
                          <path
                            d="M9.99999 5.33334V9.33334L12 10.3333M16 9.33334C16 13.0152 12.9819 16.0333 9.3 16.0333C5.61807 16.0333 2.6 13.0152 2.6 9.33334C2.6 5.65143 5.61807 2.63336 9.3 2.63336C12.9819 2.63336 16 5.65143 16 9.33334Z"
                            stroke="#D5D7DA"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        {courseInfo.duration || "0 phút"}/buổi
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="mb-6 ">
                  <h2 className="text-lg font-semibold text-[#181D27] mb-4">
                    Phương thức thanh toán
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 gap-4">
                    <button
                      onClick={handlePayment}
                      disabled={isLoading}
                      className="w-full flex flex-col items-center p-4 border-2 border-[#299D55] focus:border-2 focus:border-[#299D55] rounded-xl bg-[#FFFFFF] relative"
                    >
                      <div className="flex flex-col items-center gap-4 h-[96px] max-md:h-[120px]">
                        <div className="bg-[#FFFFFF] rounded-xl">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="32"
                            height="32"
                            viewBox="0 0 32 32"
                            fill="none"
                          >
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M22.3333 31.3333H28.3333C29.9903 31.3333 31.3333 29.9903 31.3333 28.3333V22.3333C31.3333 21.7813 30.8853 21.3333 30.3333 21.3333C29.7813 21.3333 29.3333 21.7813 29.3333 22.3333V28.3333C29.3333 28.8853 28.8853 29.3333 28.3333 29.3333H22.3333C21.7813 29.3333 21.3333 29.7813 21.3333 30.3333C21.3333 30.8853 21.7813 31.3333 22.3333 31.3333ZM10.3333 29.3333H4.33331C3.78131 29.3333 3.33331 28.8853 3.33331 28.3333V22.3333C3.33331 21.7813 2.88531 21.3333 2.33331 21.3333C1.78131 21.3333 1.33331 21.7813 1.33331 22.3333V28.3333C1.33331 29.9903 2.67631 31.3333 4.33331 31.3333H10.3333C10.8853 31.3333 11.3333 30.8853 11.3333 30.3333C11.3333 29.7813 10.8853 29.3333 10.3333 29.3333ZM15.3333 20.3333V26.3333C15.3333 26.8853 15.7813 27.3333 16.3333 27.3333C16.8853 27.3333 17.3333 26.8853 17.3333 26.3333V20.3333C17.3333 19.7813 16.8853 19.3333 16.3333 19.3333C15.7813 19.3333 15.3333 19.7813 15.3333 20.3333ZM13.3333 20.3333C13.3333 19.7813 12.8853 19.3333 12.3333 19.3333H6.33331C5.78131 19.3333 5.33331 19.7813 5.33331 20.3333V26.3333C5.33331 26.8853 5.78131 27.3333 6.33331 27.3333H12.3333C12.8853 27.3333 13.3333 26.8853 13.3333 26.3333V20.3333ZM27.3333 20.3333C27.3333 19.7813 26.8853 19.3333 26.3333 19.3333H20.3333C19.7813 19.3333 19.3333 19.7813 19.3333 20.3333V26.3333C19.3333 26.8853 19.7813 27.3333 20.3333 27.3333H26.3333C26.8853 27.3333 27.3333 26.8853 27.3333 26.3333V20.3333ZM11.3333 21.3333V25.3333H7.33331V21.3333H11.3333ZM25.3333 21.3333V25.3333H21.3333V21.3333H25.3333ZM15.3333 17.3333H16.3333C16.8853 17.3333 17.3333 16.8853 17.3333 16.3333C17.3333 15.7813 16.8853 15.3333 16.3333 15.3333H15.3333C14.7813 15.3333 14.3333 15.7813 14.3333 16.3333C14.3333 16.8853 14.7813 17.3333 15.3333 17.3333ZM6.33331 17.3333H11.3333C11.8853 17.3333 12.3333 16.8853 12.3333 16.3333C12.3333 15.7813 11.8853 15.3333 11.3333 15.3333H6.33331C5.78131 15.3333 5.33331 15.7813 5.33331 16.3333C5.33331 16.8853 5.78131 17.3333 6.33331 17.3333ZM24.3333 15.3333H25.3333V16.3333C25.3333 16.8853 25.7813 17.3333 26.3333 17.3333C26.8853 17.3333 27.3333 16.8853 27.3333 16.3333V14.3333C27.3333 13.7813 26.8853 13.3333 26.3333 13.3333H24.3333C23.7813 13.3333 23.3333 13.7813 23.3333 14.3333C23.3333 14.8853 23.7813 15.3333 24.3333 15.3333ZM21.3333 16.3333V11.3333H22.3333C22.8853 11.3333 23.3333 10.8853 23.3333 10.3333C23.3333 9.78131 22.8853 9.33331 22.3333 9.33331H20.3333C19.7813 9.33331 19.3333 9.78131 19.3333 10.3333V16.3333C19.3333 16.8853 19.7813 17.3333 20.3333 17.3333C20.8853 17.3333 21.3333 16.8853 21.3333 16.3333ZM15.3333 10.3333V12.3333C15.3333 12.8853 15.7813 13.3333 16.3333 13.3333C16.8853 13.3333 17.3333 12.8853 17.3333 12.3333V10.3333C17.3333 9.78131 16.8853 9.33331 16.3333 9.33331C15.7813 9.33331 15.3333 9.78131 15.3333 10.3333ZM13.3333 6.33331C13.3333 5.78131 12.8853 5.33331 12.3333 5.33331H6.33331C5.78131 5.33331 5.33331 5.78131 5.33331 6.33331V12.3333C5.33331 12.8853 5.78131 13.3333 6.33331 13.3333H12.3333C12.8853 13.3333 13.3333 12.8853 13.3333 12.3333V6.33331ZM10.3333 1.33331H4.33331C2.67631 1.33331 1.33331 2.67631 1.33331 4.33331V10.3333C1.33331 10.8853 1.78131 11.3333 2.33331 11.3333C2.88531 11.3333 3.33331 10.8853 3.33331 10.3333V4.33331C3.33331 3.78131 3.78131 3.33331 4.33331 3.33331H10.3333C10.8853 3.33331 11.3333 2.88531 11.3333 2.33331C11.3333 1.78131 10.8853 1.33331 10.3333 1.33331ZM11.3333 7.33331V11.3333H7.33331V7.33331H11.3333ZM22.3333 3.33331H28.3333C28.8853 3.33331 29.3333 3.78131 29.3333 4.33331V10.3333C29.3333 10.8853 29.7813 11.3333 30.3333 11.3333C30.8853 11.3333 31.3333 10.8853 31.3333 10.3333V4.33331C31.3333 2.67631 29.9903 1.33331 28.3333 1.33331H22.3333C21.7813 1.33331 21.3333 1.78131 21.3333 2.33331C21.3333 2.88531 21.7813 3.33331 22.3333 3.33331ZM16.3333 7.33331H25.3333V10.3333C25.3333 10.8853 25.7813 11.3333 26.3333 11.3333C26.8853 11.3333 27.3333 10.8853 27.3333 10.3333V6.33331C27.3333 5.78131 26.8853 5.33331 26.3333 5.33331H16.3333C15.7813 5.33331 15.3333 5.78131 15.3333 6.33331C15.3333 6.88531 15.7813 7.33331 16.3333 7.33331Z"
                              fill="black"
                            />
                          </svg>
                        </div>
                        <span className="text-sm text-center">
                          Quét mã QR thông qua ứng dụng Ngân Hàng hoặc ví điện
                          tử Momo, ZaloPay
                        </span>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Discount Code */}
                <div className="mb-6 ">
                  <h2 className="text-lg font-semibold text-[#181D27] mb-4">
                    Ưu đãi
                  </h2>
                  <div className="flex flex-col gap-2">
                    <div className="flex gap-2 items-start">
                      <TextField
                        placeholder="Nhập mã ưu đãi"
                        name="discountCode"
                        className="flex-1"
                        value={discountCode}
                        onChange={handleDiscountCodeChange}
                        onClear={
                          voucherApplied ? null : handleClearDiscountCode
                        }
                        error={discountError}
                        disabled={voucherApplied}
                      />
                      <div className="flex-shrink-0 self-start pt-[1px]">
                        <Button
                          variant="primary"
                          className="h-[42px] min-w-[90px]"
                          onClick={handleApplyVoucher}
                          disabled={isApplyingVoucher || !discountCode.trim()}
                        >
                          {isApplyingVoucher
                            ? "Đang áp dụng..."
                            : voucherApplied
                            ? "Đã áp dụng"
                            : "Áp dụng"}
                        </Button>
                      </div>
                    </div>

                    {/* Hiển thị số lượt sử dụng còn lại */}
                    {voucherApplied && remainingUses !== null && (
                      <div className="text-sm text-green-600 mt-1">
                        Còn lại {remainingUses} lượt sử dụng
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex justify-between gap-2">
                  <p className="text-base text-[#181D27] font-normal">
                    Học phí
                  </p>
                  <p className="text-base text-[#181D27] font-normal">
                    {(courseInfo.price || 0).toLocaleString("vi-VN")}đ
                  </p>
                </div>
                {voucherApplied && discountAmount > 0 && (
                  <div className="flex items-center justify-between mt-2">
                    <p className="text-base font-normal text-[#181D27]">
                      Giảm giá
                    </p>
                    <p className="text-base font-normal text-green-600">
                      -{(discountAmount || 0).toLocaleString("vi-VN")}đ
                    </p>
                  </div>
                )}
                <div className="border-b border-[#E9EAEB] my-4"></div>
                <div className="flex justify-between gap-2">
                  <p className="text-base text-[#181D27] font-semibold">
                    Tổng thanh toán
                  </p>
                  <p className="text-base text-[#181D27] font-semibold">
                    {calculateFinalPrice().toLocaleString("vi-VN")}đ
                  </p>
                </div>
                <Button
                  variant="primary"
                  className="w-full mt-6"
                  onClick={handlePayment}
                  disabled={!isFormValid || isLoading}
                >
                  {isLoading ? "Đang xử lý..." : "Tiến hành thanh toán"}
                </Button>
              </div>
            </div>
          </div>
        </div>
        {/* Fixed bottom button for mobile/tablet */}
        <div className="md:hidden fixed bottom-0 left-0 right-0 bg-[#FFFFFF] px-4 py-3 border-t border-[#E9EAEB]">
          <div className="flex justify-between mb-4">
            <span className="text-[#181D27] text-base font-semibold">
              Tổng thanh toán
            </span>
            <span className="text-[#181D27] text-base font-semibold">
              {calculateFinalPrice().toLocaleString("vi-VN")}đ
            </span>
          </div>
          <Button
            variant="primary"
            className="w-full"
            onClick={handlePayment}
            disabled={!isFormValid || isLoading}
          >
            {isLoading ? "Đang xử lý..." : "Tiến hành thanh toán"}
          </Button>
        </div>
      </div>
    </div>
  );
}

// Main component with Suspense boundary
const CheckoutPage = () => {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#299D55]"></div>
        </div>
      }
    >
      <CheckoutContent />
    </Suspense>
  );
};

export default CheckoutPage;
